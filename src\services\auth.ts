import { api, setAuthToken, removeAuthToken } from './api';

export interface LoginResponse {
  success: boolean;
  token: string;
  user: {
    isAdmin: boolean;
    houseId: string;
    username: string;
    houseName: string;
    roommateId?: string;
    roommateName?: string;
  };
}

export interface VerifyResponse {
  success: boolean;
  user: {
    isAdmin: boolean;
    houseId: string;
    username: string;
    houseName: string;
    roommateId?: string;
    roommateName?: string;
  };
}

// Admin login
export async function adminLogin(username: string, password: string): Promise<LoginResponse> {
  const response = await api.post<LoginResponse>('/auth/admin/login', {
    username,
    password,
  });

  if (response.success && response.token) {
    setAuthToken(response.token);
  }

  return response;
}

// Roommate login
export async function roommateLogin(username: string, roommateName: string): Promise<LoginResponse> {
  const response = await api.post<LoginResponse>('/auth/roommate/login', {
    username,
    roommateName,
  });

  if (response.success && response.token) {
    setAuthToken(response.token);
  }

  return response;
}

// Logout
export async function logout(): Promise<void> {
  try {
    await api.post('/auth/logout');
  } catch (error) {
    console.error('Logout error:', error);
  } finally {
    removeAuthToken();
    // Clear other localStorage items
    localStorage.removeItem('currentRoommate');
    localStorage.removeItem('house');
    localStorage.removeItem('roommates');
    localStorage.removeItem('expenses');
    localStorage.removeItem('adminLoggedIn');
  }
}

// Verify token
export async function verifyToken(): Promise<VerifyResponse | null> {
  try {
    const response = await api.get<VerifyResponse>('/auth/verify');
    return response;
  } catch (error) {
    console.error('Token verification failed:', error);
    removeAuthToken();
    return null;
  }
}
