import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseApiOptions {
  immediate?: boolean;
  dependencies?: any[];
}

export function useApi<T>(
  apiCall: () => Promise<T>,
  options: UseApiOptions = {}
) {
  const { immediate = true, dependencies = [] } = options;
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: immediate,
    error: null,
  });
  const { logout } = useAuth();

  const execute = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const result = await apiCall();
      setState({ data: result, loading: false, error: null });
      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'An error occurred';
      setState({ data: null, loading: false, error: errorMessage });
      
      // If it's an authentication error, logout the user
      if (error.message?.includes('token') || error.message?.includes('auth')) {
        logout();
      }
      
      throw error;
    }
  };

  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, dependencies);

  return {
    ...state,
    execute,
    refetch: execute,
  };
}

// Specialized hook for mutations (POST, PUT, DELETE)
export function useMutation<T, P = any>(
  apiCall: (params: P) => Promise<T>
) {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });
  const { logout } = useAuth();

  const mutate = async (params: P) => {
    setState({ data: null, loading: true, error: null });
    
    try {
      const result = await apiCall(params);
      setState({ data: result, loading: false, error: null });
      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'An error occurred';
      setState({ data: null, loading: false, error: errorMessage });
      
      // If it's an authentication error, logout the user
      if (error.message?.includes('token') || error.message?.includes('auth')) {
        logout();
      }
      
      throw error;
    }
  };

  return {
    ...state,
    mutate,
  };
}
