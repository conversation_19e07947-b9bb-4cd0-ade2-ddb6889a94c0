# House Expenser Backend API

A Node.js/Express.js backend API for the House Expenser application - a roommate expense tracking and splitting system.

## Features

- 🏠 House management with admin authentication
- 👥 Roommate management
- 💰 Expense tracking with flexible splitting
- 📊 Balance calculations and settlement suggestions
- 🔐 JWT-based authentication
- 🗄️ SQLite database with automatic migrations
- ⚡ RESTful API design
- 🛡️ Security middleware (helmet, rate limiting, CORS)

## Quick Start

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Navigate to the Backend directory:
```bash
cd Backend
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Initialize the database:
```bash
npm run init-db
```

5. Start the development server:
```bash
npm run dev
```

The API will be available at `http://localhost:3001`

## API Endpoints

### Authentication
- `POST /api/auth/admin/login` - Admin login
- `POST /api/auth/roommate/login` - Roommate login
- `POST /api/auth/logout` - Logout
- `GET /api/auth/verify` - Verify token

### Houses
- `POST /api/houses` - Create house
- `GET /api/houses/:id` - Get house details
- `PUT /api/houses/:id` - Update house (admin only)
- `DELETE /api/houses/:id` - Delete house (admin only)
- `GET /api/houses/:id/stats` - Get house statistics (admin only)

### Roommates
- `GET /api/roommates/house/:houseId` - Get all roommates in house
- `POST /api/roommates` - Create roommate (admin only)
- `GET /api/roommates/:id` - Get roommate details
- `PUT /api/roommates/:id` - Update roommate (admin only)
- `DELETE /api/roommates/:id` - Delete roommate (admin only)

### Expenses
- `GET /api/expenses/house/:houseId` - Get all expenses for house
- `POST /api/expenses` - Create expense
- `GET /api/expenses/:id` - Get expense details
- `DELETE /api/expenses/:id` - Delete expense (admin or creator only)

### Balances
- `GET /api/balances/house/:houseId` - Get balances and settlements for house
- `GET /api/balances/roommate/:roommateId` - Get balance for specific roommate
- `GET /api/balances/summary/:houseId` - Get expense summary for house

## Database Schema

### Tables

- **houses** - House information and admin credentials
- **roommates** - Roommate information linked to houses
- **expenses** - Expense records with payer information
- **expense_participants** - Split expense details for each participant
- **sessions** - Authentication sessions

## Authentication

The API uses JWT tokens for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Admin Authentication
Admins authenticate with house ID and password to manage house settings and roommates.

### Roommate Authentication
Roommates authenticate with house ID and their name (no password required).

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | 3001 |
| `NODE_ENV` | Environment | development |
| `FRONTEND_URL` | Frontend URL for CORS | http://localhost:5173 |
| `JWT_SECRET` | JWT signing secret | (required) |
| `DB_PATH` | SQLite database path | ./data/house_expenser.db |

## Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run init-db` - Initialize database tables
- `npm test` - Run tests

## Security Features

- Helmet.js for security headers
- Rate limiting to prevent abuse
- CORS configuration
- JWT token expiration
- Password hashing with bcrypt
- Input validation with express-validator

## Error Handling

The API returns consistent error responses:

```json
{
  "error": "Error Type",
  "message": "Detailed error message",
  "details": [] // For validation errors
}
```

## Development

### Adding New Routes

1. Create route file in `src/routes/`
2. Import and use in `src/server.js`
3. Add authentication middleware as needed

### Database Queries

Use the helper functions from `src/database/init.js`:
- `runQuery(query, params)` - For INSERT, UPDATE, DELETE
- `getRow(query, params)` - For single row SELECT
- `getAllRows(query, params)` - For multiple row SELECT

## Production Deployment

1. Set `NODE_ENV=production`
2. Use a strong `JWT_SECRET`
3. Configure proper CORS origins
4. Set up reverse proxy (nginx)
5. Use process manager (PM2)
6. Set up SSL/TLS

## License

MIT
