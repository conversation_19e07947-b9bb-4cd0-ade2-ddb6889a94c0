export interface House {
  id: string;
  name: string;
  currency: string;
  createdAt: Date;
}

export interface Roommate {
  id: string;
  name: string;
  houseId: string;
  createdAt: Date;
}

export interface Expense {
  id: string;
  amount: number;
  description: string;
  date: Date;
  payerId: string;
  participants: ExpenseParticipant[];
  loggedBy: string;
  loggedAt: Date;
  houseId: string;
}

export interface ExpenseParticipant {
  roommateId: string;
  amount: number;
}

export interface Balance {
  roommateId: string;
  roommateName: string;
  balance: number;
}

export interface Settlement {
  from: string;
  to: string;
  fromName: string;
  toName: string;
  amount: number;
}

export interface User {
  id: string;
  username: string;
  isAdmin: boolean;
}