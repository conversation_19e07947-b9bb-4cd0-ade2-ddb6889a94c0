@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 220 15% 97%;
    --foreground: 215 25% 15%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 15%;

    --primary: 210 85% 45%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 210 85% 65%;

    --secondary: 165 65% 45%;
    --secondary-foreground: 0 0% 100%;
    --secondary-glow: 165 65% 65%;

    --muted: 220 15% 95%;
    --muted-foreground: 215 15% 45%;

    --accent: 35 90% 65%;
    --accent-foreground: 215 25% 15%;

    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 15% 90%;
    --input: 220 15% 95%;
    --ring: 210 85% 45%;

    --success: 140 65% 45%;
    --success-foreground: 0 0% 100%;
    
    --warning: 35 90% 55%;
    --warning-foreground: 215 25% 15%;

    /* Gradients - Fixed HSL syntax */
    --gradient-primary: linear-gradient(135deg, hsl(210 85% 45%), hsl(210 85% 65%));
    --gradient-secondary: linear-gradient(135deg, hsl(165 65% 45%), hsl(165 65% 65%));
    --gradient-warm: linear-gradient(135deg, hsl(35 90% 65%), hsl(165 65% 65%));
    
    /* Shadows */
    --shadow-card: 0 4px 20px -2px hsl(210 85% 45% / 0.1);
    --shadow-button: 0 2px 8px -1px hsl(210 85% 45% / 0.2);
    --shadow-glow: 0 0 30px hsl(210 85% 65% / 0.3);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}