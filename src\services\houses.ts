import { api } from './api';
import { House } from '../types';

export interface CreateHouseRequest {
  username: string;
  name: string;
  currency: string;
  adminPassword: string;
}

export interface CreateHouseResponse {
  success: boolean;
  house: House;
}

export interface GetHouseResponse {
  success: boolean;
  house: House;
}

export interface UpdateHouseRequest {
  name?: string;
  currency?: string;
}

export interface HouseStatsResponse {
  success: boolean;
  stats: {
    roommateCount: number;
    expenseCount: number;
    totalExpenses: number;
  };
}

// Create a new house
export async function createHouse(data: CreateHouseRequest): Promise<CreateHouseResponse> {
  return api.post<CreateHouseResponse>('/houses', data);
}

// Get house by ID
export async function getHouse(houseId: string): Promise<GetHouseResponse> {
  return api.get<GetHouseResponse>(`/houses/${houseId}`);
}

// Update house (admin only)
export async function updateHouse(houseId: string, data: UpdateHouseRequest): Promise<GetHouseResponse> {
  return api.put<GetHouseResponse>(`/houses/${houseId}`, data);
}

// Delete house (admin only)
export async function deleteHouse(houseId: string): Promise<{ success: boolean; message: string }> {
  return api.delete(`/houses/${houseId}`);
}

// Get house statistics (admin only)
export async function getHouseStats(houseId: string): Promise<HouseStatsResponse> {
  return api.get<HouseStatsResponse>(`/houses/${houseId}/stats`);
}
