import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Balance, Settlement } from '@/types';
import { calculateSettlements, formatCurrency } from '@/utils/calculations';

interface SettlementViewProps {
  balances: Balance[];
  settlements?: Settlement[];
  currency: string;
  onClose: () => void;
}

export const SettlementView = ({ balances, settlements: providedSettlements, currency, onClose }: SettlementViewProps) => {
  const settlements = providedSettlements || calculateSettlements([...balances]);
  
  return (
    <Card className="shadow-card">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            💰 Settle Up
          </span>
          <Button variant="ghost" onClick={onClose} size="sm">
            ✕
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {settlements.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-4xl mb-2">🎉</div>
            <h3 className="text-lg font-semibold mb-2">All settled up!</h3>
            <p className="text-muted-foreground">
              Everyone is even. No transfers needed.
            </p>
          </div>
        ) : (
          <>
            <p className="text-sm text-muted-foreground mb-4">
              Optimal transfers to settle all balances:
            </p>
            <div className="space-y-3">
              {settlements.map((settlement, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="font-medium">
                      {settlement.fromName}
                    </Badge>
                    <span className="text-muted-foreground">→</span>
                    <Badge variant="outline" className="font-medium">
                      {settlement.toName}
                    </Badge>
                  </div>
                  <Badge variant="secondary" className="font-mono text-sm">
                    {formatCurrency(settlement.amount, currency)}
                  </Badge>
                </div>
              ))}
            </div>
            <div className="pt-4 border-t">
              <p className="text-xs text-muted-foreground">
                💡 These are the minimum transfers needed to settle all balances.
              </p>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};