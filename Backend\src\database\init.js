import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Database file path
const DB_PATH = join(__dirname, '..', '..', 'data', 'house_expenser.db');

// Ensure data directory exists
async function ensureDataDirectory() {
  const dataDir = join(__dirname, '..', '..', 'data');
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// Get database connection
export function getDatabase() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH, (err) => {
      if (err) {
        reject(err);
      } else {
        resolve(db);
      }
    });
  });
}

// Initialize database with tables
export async function initializeDatabase() {
  await ensureDataDirectory();
  const db = await getDatabase();

  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Houses table
      db.run(`
        CREATE TABLE IF NOT EXISTS houses (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          username TEXT UNIQUE,
          currency TEXT NOT NULL DEFAULT '₺',
          admin_password_hash TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Roommates table
      db.run(`
        CREATE TABLE IF NOT EXISTS roommates (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          house_id TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (house_id) REFERENCES houses (id) ON DELETE CASCADE
        )
      `);

      // Expenses table
      db.run(`
        CREATE TABLE IF NOT EXISTS expenses (
          id TEXT PRIMARY KEY,
          amount REAL NOT NULL,
          description TEXT NOT NULL,
          date DATETIME NOT NULL,
          payer_id TEXT NOT NULL,
          logged_by TEXT NOT NULL,
          logged_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          house_id TEXT NOT NULL,
          is_paid BOOLEAN DEFAULT FALSE,
          paid_at DATETIME,
          paid_by TEXT,
          FOREIGN KEY (payer_id) REFERENCES roommates (id),
          FOREIGN KEY (house_id) REFERENCES houses (id) ON DELETE CASCADE
        )
      `);

      // Expense participants table (for split expenses)
      db.run(`
        CREATE TABLE IF NOT EXISTS expense_participants (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          expense_id TEXT NOT NULL,
          roommate_id TEXT NOT NULL,
          amount REAL NOT NULL,
          FOREIGN KEY (expense_id) REFERENCES expenses (id) ON DELETE CASCADE,
          FOREIGN KEY (roommate_id) REFERENCES roommates (id)
        )
      `);

      // Sessions table for authentication
      db.run(`
        CREATE TABLE IF NOT EXISTS sessions (
          id TEXT PRIMARY KEY,
          house_id TEXT NOT NULL,
          roommate_id TEXT,
          is_admin BOOLEAN DEFAULT FALSE,
          expires_at DATETIME NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (house_id) REFERENCES houses (id) ON DELETE CASCADE,
          FOREIGN KEY (roommate_id) REFERENCES roommates (id) ON DELETE CASCADE
        )
      `);

      // Create indexes for better performance
      db.run(`CREATE INDEX IF NOT EXISTS idx_houses_username ON houses (username)`);
      db.run(`CREATE INDEX IF NOT EXISTS idx_roommates_house_id ON roommates (house_id)`);
      db.run(`CREATE INDEX IF NOT EXISTS idx_expenses_house_id ON expenses (house_id)`);
      db.run(`CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses (date)`);
      db.run(`CREATE INDEX IF NOT EXISTS idx_expenses_is_paid ON expenses (is_paid)`);
      db.run(`CREATE INDEX IF NOT EXISTS idx_expense_participants_expense_id ON expense_participants (expense_id)`);
      db.run(`CREATE INDEX IF NOT EXISTS idx_sessions_house_id ON sessions (house_id)`);
      db.run(`CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON sessions (expires_at)`);

      db.close((err) => {
        if (err) {
          reject(err);
        } else {
          console.log('✅ Database tables created successfully');
          resolve();
        }
      });
    });
  });
}

// Helper function to run database queries
export function runQuery(query, params = []) {
  return new Promise(async (resolve, reject) => {
    const db = await getDatabase();
    db.run(query, params, function(err) {
      db.close();
      if (err) {
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
  });
}

// Helper function to get single row
export function getRow(query, params = []) {
  return new Promise(async (resolve, reject) => {
    const db = await getDatabase();
    db.get(query, params, (err, row) => {
      db.close();
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

// Helper function to get all rows
export function getAllRows(query, params = []) {
  return new Promise(async (resolve, reject) => {
    const db = await getDatabase();
    db.all(query, params, (err, rows) => {
      db.close();
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}
