import jwt from 'jsonwebtoken';
import { getRow } from '../database/init.js';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// Authenticate JWT token
export async function authenticateToken(req, res, next) {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: 'Access token required'
      });
    }

    // Verify JWT token
    const decoded = jwt.verify(token, JWT_SECRET);

    // Check if session exists and is not expired
    const session = await getRow(
      'SELECT * FROM sessions WHERE id = ? AND expires_at > datetime("now")',
      [decoded.sessionId]
    );

    if (!session) {
      return res.status(401).json({
        error: 'Session expired or invalid'
      });
    }

    // Add user info to request
    req.user = {
      sessionId: decoded.sessionId,
      houseId: session.house_id,
      roommateId: session.roommate_id,
      isAdmin: session.is_admin
    };

    // If not admin, get roommate name
    if (!session.is_admin && session.roommate_id) {
      const roommate = await getRow(
        'SELECT name FROM roommates WHERE id = ?',
        [session.roommate_id]
      );
      
      if (roommate) {
        req.user.roommateName = roommate.name;
      }
    }

    next();

  } catch (error) {
    console.error('Authentication error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Invalid token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Token expired'
      });
    }

    return res.status(500).json({
      error: 'Authentication failed'
    });
  }
}

// Require admin privileges
export function requireAdmin(req, res, next) {
  if (!req.user || !req.user.isAdmin) {
    return res.status(403).json({
      error: 'Admin privileges required'
    });
  }
  next();
}

// Require user to be in specific house
export function requireHouseAccess(houseId) {
  return (req, res, next) => {
    if (!req.user || req.user.houseId !== houseId) {
      return res.status(403).json({
        error: 'Access denied to this house'
      });
    }
    next();
  };
}

// Optional authentication (doesn't fail if no token)
export async function optionalAuth(req, res, next) {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return next();
    }

    const decoded = jwt.verify(token, JWT_SECRET);
    const session = await getRow(
      'SELECT * FROM sessions WHERE id = ? AND expires_at > datetime("now")',
      [decoded.sessionId]
    );

    if (session) {
      req.user = {
        sessionId: decoded.sessionId,
        houseId: session.house_id,
        roommateId: session.roommate_id,
        isAdmin: session.is_admin
      };

      if (!session.is_admin && session.roommate_id) {
        const roommate = await getRow(
          'SELECT name FROM roommates WHERE id = ?',
          [session.roommate_id]
        );
        
        if (roommate) {
          req.user.roommateName = roommate.name;
        }
      }
    }

    next();

  } catch (error) {
    // Ignore authentication errors for optional auth
    next();
  }
}
