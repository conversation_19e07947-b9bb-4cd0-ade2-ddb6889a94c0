import express from 'express';
import { getAllRows } from '../database/init.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// Calculate balances for a house
router.get('/house/:houseId', authenticateToken, async (req, res) => {
  try {
    const { houseId } = req.params;

    // Check if user has access to this house
    if (req.user.houseId !== houseId) {
      return res.status(403).json({
        error: 'Access denied to this house'
      });
    }

    // Get all roommates in the house
    const roommates = await getAllRows(
      'SELECT id, name FROM roommates WHERE house_id = ?',
      [houseId]
    );

    if (roommates.length === 0) {
      return res.json({
        success: true,
        balances: [],
        settlements: []
      });
    }

    // Initialize balances
    const balances = {};
    roommates.forEach(roommate => {
      balances[roommate.id] = {
        roommateId: roommate.id,
        roommateName: roommate.name,
        balance: 0
      };
    });

    // Get all expenses and their participants
    const expenses = await getAllRows(`
      SELECT 
        e.id,
        e.amount,
        e.payer_id,
        ep.roommate_id as participant_id,
        ep.amount as participant_amount
      FROM expenses e
      JOIN expense_participants ep ON e.id = ep.expense_id
      WHERE e.house_id = ?
    `, [houseId]);

    // Calculate balances
    expenses.forEach(expense => {
      const payerId = expense.payer_id;
      const participantId = expense.participant_id;
      const participantAmount = expense.participant_amount;

      // Payer gets credited for the full amount they paid
      if (balances[payerId]) {
        balances[payerId].balance += expense.amount;
      }

      // Each participant gets debited for their share
      if (balances[participantId]) {
        balances[participantId].balance -= participantAmount;
      }
    });

    // Convert to array and filter out roommates with zero balance
    const balanceArray = Object.values(balances);

    // Calculate settlements (who owes whom)
    const settlements = calculateSettlements(balanceArray);

    res.json({
      success: true,
      balances: balanceArray,
      settlements
    });

  } catch (error) {
    console.error('Get balances error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Calculate optimal settlements
function calculateSettlements(balances) {
  const settlements = [];
  
  // Separate debtors (negative balance) and creditors (positive balance)
  const debtors = balances.filter(b => b.balance < -0.01).map(b => ({ ...b }));
  const creditors = balances.filter(b => b.balance > 0.01).map(b => ({ ...b }));

  // Sort by absolute balance amount (largest first)
  debtors.sort((a, b) => a.balance - b.balance); // Most negative first
  creditors.sort((a, b) => b.balance - a.balance); // Most positive first

  let debtorIndex = 0;
  let creditorIndex = 0;

  while (debtorIndex < debtors.length && creditorIndex < creditors.length) {
    const debtor = debtors[debtorIndex];
    const creditor = creditors[creditorIndex];

    const debtAmount = Math.abs(debtor.balance);
    const creditAmount = creditor.balance;

    const settlementAmount = Math.min(debtAmount, creditAmount);

    if (settlementAmount > 0.01) {
      settlements.push({
        from: debtor.roommateId,
        fromName: debtor.roommateName,
        to: creditor.roommateId,
        toName: creditor.roommateName,
        amount: Math.round(settlementAmount * 100) / 100 // Round to 2 decimal places
      });

      // Update balances
      debtor.balance += settlementAmount;
      creditor.balance -= settlementAmount;
    }

    // Move to next debtor or creditor if current one is settled
    if (Math.abs(debtor.balance) < 0.01) {
      debtorIndex++;
    }
    if (Math.abs(creditor.balance) < 0.01) {
      creditorIndex++;
    }
  }

  return settlements;
}

// Get balance for a specific roommate
router.get('/roommate/:roommateId', authenticateToken, async (req, res) => {
  try {
    const { roommateId } = req.params;

    // Get roommate to check house access
    const roommateResult = await getAllRows(
      'SELECT id, name, house_id FROM roommates WHERE id = ?',
      [roommateId]
    );

    const roommate = roommateResult[0];

    if (!roommate) {
      return res.status(404).json({
        error: 'Roommate not found'
      });
    }

    // Check if user has access to this house
    if (req.user.houseId !== roommate.house_id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    // Calculate balance for this specific roommate
    let balance = 0;

    // Get all expenses where this roommate was the payer
    const paidExpenses = await getAllRows(
      'SELECT amount FROM expenses WHERE payer_id = ? AND house_id = ?',
      [roommateId, roommate.house_id]
    );

    // Add amounts paid
    paidExpenses.forEach(expense => {
      balance += expense.amount;
    });

    // Get all expense participations for this roommate
    const participations = await getAllRows(`
      SELECT ep.amount 
      FROM expense_participants ep
      JOIN expenses e ON ep.expense_id = e.id
      WHERE ep.roommate_id = ? AND e.house_id = ?
    `, [roommateId, roommate.house_id]);

    // Subtract amounts owed
    participations.forEach(participation => {
      balance -= participation.amount;
    });

    res.json({
      success: true,
      balance: {
        roommateId: roommate.id,
        roommateName: roommate.name,
        balance: Math.round(balance * 100) / 100 // Round to 2 decimal places
      }
    });

  } catch (error) {
    console.error('Get roommate balance error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Get expense summary for a house
router.get('/summary/:houseId', authenticateToken, async (req, res) => {
  try {
    const { houseId } = req.params;

    // Check if user has access to this house
    if (req.user.houseId !== houseId) {
      return res.status(403).json({
        error: 'Access denied to this house'
      });
    }

    // Get total expenses
    const totalExpensesResult = await getAllRows(
      'SELECT COALESCE(SUM(amount), 0) as total, COUNT(*) as count FROM expenses WHERE house_id = ?',
      [houseId]
    );
    const totalExpenses = totalExpensesResult[0];

    // Get expenses by roommate (who paid)
    const expensesByPayer = await getAllRows(`
      SELECT 
        r.id,
        r.name,
        COALESCE(SUM(e.amount), 0) as total_paid,
        COUNT(e.id) as expense_count
      FROM roommates r
      LEFT JOIN expenses e ON r.id = e.payer_id
      WHERE r.house_id = ?
      GROUP BY r.id, r.name
      ORDER BY total_paid DESC
    `, [houseId]);

    // Get recent expenses (last 10)
    const recentExpenses = await getAllRows(`
      SELECT 
        e.id,
        e.amount,
        e.description,
        e.date,
        e.logged_by,
        p.name as payer_name
      FROM expenses e
      LEFT JOIN roommates p ON e.payer_id = p.id
      WHERE e.house_id = ?
      ORDER BY e.date DESC, e.logged_at DESC
      LIMIT 10
    `, [houseId]);

    res.json({
      success: true,
      summary: {
        totalAmount: totalExpenses.total,
        totalCount: totalExpenses.count,
        expensesByPayer: expensesByPayer.map(payer => ({
          roommateId: payer.id,
          roommateName: payer.name,
          totalPaid: payer.total_paid,
          expenseCount: payer.expense_count
        })),
        recentExpenses: recentExpenses.map(expense => ({
          id: expense.id,
          amount: expense.amount,
          description: expense.description,
          date: expense.date,
          payerName: expense.payer_name,
          loggedBy: expense.logged_by
        }))
      }
    });

  } catch (error) {
    console.error('Get expense summary error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

export default router;
