import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Expense, Roommate } from '@/types';
import { formatCurrency } from '@/utils/calculations';

interface ActivityFeedProps {
  expenses: Expense[];
  roommates: Roommate[];
  currency: string;
}

export const ActivityFeed = ({ expenses, roommates, currency }: ActivityFeedProps) => {
  const getRoommateName = (id: string) => 
    roommates.find(r => r.id === id)?.name || 'Unknown';

  const formatDate = (date: Date) => {
    const d = new Date(date);
    return d.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card className="shadow-card">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          📋 Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {expenses.length === 0 ? (
          <p className="text-muted-foreground text-center py-4">
            No expenses yet. Add your first expense to get started!
          </p>
        ) : (
          expenses
            .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
            .map((expense) => (
              <div key={expense.id} className="border-l-4 border-primary pl-4 py-2">
                <div className="flex justify-between items-start mb-1">
                  <span className="font-medium text-sm">
                    💳 {formatCurrency(expense.amount, currency)} – {expense.description}
                  </span>
                  <Badge variant="outline" className="text-xs">
                    {formatDate(expense.date)}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground">
                  Paid by <span className="font-medium">{getRoommateName(expense.payerId)}</span>, 
                  split {expense.participants.length} way{expense.participants.length > 1 ? 's' : ''}
                </p>
                <p className="text-xs text-muted-foreground">
                  Logged by <span className="font-medium">{expense.loggedBy}</span> • {formatDate(expense.loggedAt)}
                </p>
              </div>
            ))
        )}
      </CardContent>
    </Card>
  );
};