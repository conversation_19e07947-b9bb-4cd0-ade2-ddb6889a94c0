import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { body, validationResult } from 'express-validator';
import { getRow, runQuery, getAllRows } from '../database/init.js';
import { authenticateToken, requireAdmin } from '../middleware/auth.js';

const router = express.Router();

// Validation middleware
const validateCreateRoommate = [
  body('name').trim().isLength({ min: 1 }).withMessage('Roommate name is required'),
  body('houseId').notEmpty().withMessage('House ID is required')
];

const validateUpdateRoommate = [
  body('name').optional().trim().isLength({ min: 1 }).withMessage('Roommate name cannot be empty')
];

// Get all roommates for a house
router.get('/house/:houseId', authenticateToken, async (req, res) => {
  try {
    const { houseId } = req.params;

    // Check if user has access to this house
    if (req.user.houseId !== houseId) {
      return res.status(403).json({
        error: 'Access denied to this house'
      });
    }

    const roommates = await getAllRows(
      'SELECT id, name, house_id, created_at FROM roommates WHERE house_id = ? ORDER BY created_at ASC',
      [houseId]
    );

    res.json({
      success: true,
      roommates: roommates.map(roommate => ({
        id: roommate.id,
        name: roommate.name,
        houseId: roommate.house_id,
        createdAt: roommate.created_at
      }))
    });

  } catch (error) {
    console.error('Get roommates error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Create a new roommate (admin only)
router.post('/', authenticateToken, requireAdmin, validateCreateRoommate, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { name, houseId } = req.body;

    // Check if user has access to this house
    if (req.user.houseId !== houseId) {
      return res.status(403).json({
        error: 'Access denied to this house'
      });
    }

    // Check if house exists
    const house = await getRow(
      'SELECT id FROM houses WHERE id = ?',
      [houseId]
    );

    if (!house) {
      return res.status(404).json({
        error: 'House not found'
      });
    }

    // Check if roommate name already exists in this house
    const existingRoommate = await getRow(
      'SELECT id FROM roommates WHERE house_id = ? AND name = ?',
      [houseId, name]
    );

    if (existingRoommate) {
      return res.status(400).json({
        error: 'Roommate with this name already exists in the house'
      });
    }

    const roommateId = uuidv4();

    // Create roommate
    await runQuery(
      'INSERT INTO roommates (id, name, house_id) VALUES (?, ?, ?)',
      [roommateId, name, houseId]
    );

    // Get created roommate
    const roommate = await getRow(
      'SELECT id, name, house_id, created_at FROM roommates WHERE id = ?',
      [roommateId]
    );

    res.status(201).json({
      success: true,
      roommate: {
        id: roommate.id,
        name: roommate.name,
        houseId: roommate.house_id,
        createdAt: roommate.created_at
      }
    });

  } catch (error) {
    console.error('Create roommate error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Get roommate by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const roommate = await getRow(
      'SELECT id, name, house_id, created_at FROM roommates WHERE id = ?',
      [id]
    );

    if (!roommate) {
      return res.status(404).json({
        error: 'Roommate not found'
      });
    }

    // Check if user has access to this house
    if (req.user.houseId !== roommate.house_id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    res.json({
      success: true,
      roommate: {
        id: roommate.id,
        name: roommate.name,
        houseId: roommate.house_id,
        createdAt: roommate.created_at
      }
    });

  } catch (error) {
    console.error('Get roommate error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Update roommate (admin only)
router.put('/:id', authenticateToken, requireAdmin, validateUpdateRoommate, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { id } = req.params;
    const { name } = req.body;

    // Get roommate to check house access
    const roommate = await getRow(
      'SELECT house_id FROM roommates WHERE id = ?',
      [id]
    );

    if (!roommate) {
      return res.status(404).json({
        error: 'Roommate not found'
      });
    }

    // Check if user has access to this house
    if (req.user.houseId !== roommate.house_id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    // Check if new name already exists in this house (excluding current roommate)
    if (name) {
      const existingRoommate = await getRow(
        'SELECT id FROM roommates WHERE house_id = ? AND name = ? AND id != ?',
        [roommate.house_id, name, id]
      );

      if (existingRoommate) {
        return res.status(400).json({
          error: 'Roommate with this name already exists in the house'
        });
      }
    }

    const result = await runQuery(
      'UPDATE roommates SET name = ? WHERE id = ?',
      [name, id]
    );

    if (result.changes === 0) {
      return res.status(404).json({
        error: 'Roommate not found'
      });
    }

    // Get updated roommate
    const updatedRoommate = await getRow(
      'SELECT id, name, house_id, created_at FROM roommates WHERE id = ?',
      [id]
    );

    res.json({
      success: true,
      roommate: {
        id: updatedRoommate.id,
        name: updatedRoommate.name,
        houseId: updatedRoommate.house_id,
        createdAt: updatedRoommate.created_at
      }
    });

  } catch (error) {
    console.error('Update roommate error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Delete roommate (admin only)
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Get roommate to check house access
    const roommate = await getRow(
      'SELECT house_id FROM roommates WHERE id = ?',
      [id]
    );

    if (!roommate) {
      return res.status(404).json({
        error: 'Roommate not found'
      });
    }

    // Check if user has access to this house
    if (req.user.houseId !== roommate.house_id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    // Check if roommate has any expenses
    const expenseCount = await getRow(
      'SELECT COUNT(*) as count FROM expenses WHERE payer_id = ?',
      [id]
    );

    const participantCount = await getRow(
      'SELECT COUNT(*) as count FROM expense_participants WHERE roommate_id = ?',
      [id]
    );

    if (expenseCount.count > 0 || participantCount.count > 0) {
      return res.status(400).json({
        error: 'Cannot delete roommate with existing expenses. Please remove all related expenses first.'
      });
    }

    const result = await runQuery(
      'DELETE FROM roommates WHERE id = ?',
      [id]
    );

    if (result.changes === 0) {
      return res.status(404).json({
        error: 'Roommate not found'
      });
    }

    res.json({
      success: true,
      message: 'Roommate deleted successfully'
    });

  } catch (error) {
    console.error('Delete roommate error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

export default router;
