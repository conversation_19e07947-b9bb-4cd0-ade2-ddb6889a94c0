import { getDatabase } from './init.js';

// Database migration functions
export async function runMigrations() {
  console.log('🔄 Running database migrations...');
  
  try {
    await addUsernameToHouses();
    await addPaidStatusToExpenses();
    console.log('✅ All migrations completed successfully');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

// Migration: Add username field to houses table
async function addUsernameToHouses() {
  const db = await getDatabase();
  
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Check if username column already exists
      db.get("PRAGMA table_info(houses)", (err, result) => {
        if (err) {
          db.close();
          return reject(err);
        }
        
        // Get all columns
        db.all("PRAGMA table_info(houses)", (err, columns) => {
          if (err) {
            db.close();
            return reject(err);
          }
          
          const hasUsername = columns.some(col => col.name === 'username');
          
          if (!hasUsername) {
            console.log('📝 Adding username column to houses table...');
            db.run("ALTER TABLE houses ADD COLUMN username TEXT", (err) => {
              if (err) {
                db.close();
                return reject(err);
              }
              
              // Create unique index for username
              db.run("CREATE UNIQUE INDEX IF NOT EXISTS idx_houses_username ON houses (username)", (err) => {
                db.close();
                if (err) {
                  return reject(err);
                }
                console.log('✅ Username column added to houses table');
                resolve();
              });
            });
          } else {
            db.close();
            console.log('ℹ️ Username column already exists in houses table');
            resolve();
          }
        });
      });
    });
  });
}

// Migration: Add paid status fields to expenses table
async function addPaidStatusToExpenses() {
  const db = await getDatabase();
  
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Check if is_paid column already exists
      db.all("PRAGMA table_info(expenses)", (err, columns) => {
        if (err) {
          db.close();
          return reject(err);
        }
        
        const hasIsPaid = columns.some(col => col.name === 'is_paid');
        const hasPaidAt = columns.some(col => col.name === 'paid_at');
        const hasPaidBy = columns.some(col => col.name === 'paid_by');
        
        let pendingOperations = 0;
        let completedOperations = 0;
        
        const checkComplete = () => {
          completedOperations++;
          if (completedOperations === pendingOperations) {
            db.close();
            console.log('✅ Paid status columns added to expenses table');
            resolve();
          }
        };
        
        if (!hasIsPaid) {
          pendingOperations++;
          console.log('📝 Adding is_paid column to expenses table...');
          db.run("ALTER TABLE expenses ADD COLUMN is_paid BOOLEAN DEFAULT FALSE", (err) => {
            if (err) {
              db.close();
              return reject(err);
            }
            checkComplete();
          });
        }
        
        if (!hasPaidAt) {
          pendingOperations++;
          console.log('📝 Adding paid_at column to expenses table...');
          db.run("ALTER TABLE expenses ADD COLUMN paid_at DATETIME", (err) => {
            if (err) {
              db.close();
              return reject(err);
            }
            checkComplete();
          });
        }
        
        if (!hasPaidBy) {
          pendingOperations++;
          console.log('📝 Adding paid_by column to expenses table...');
          db.run("ALTER TABLE expenses ADD COLUMN paid_by TEXT", (err) => {
            if (err) {
              db.close();
              return reject(err);
            }
            checkComplete();
          });
        }
        
        // Add index for is_paid if we added the column
        if (!hasIsPaid) {
          pendingOperations++;
          db.run("CREATE INDEX IF NOT EXISTS idx_expenses_is_paid ON expenses (is_paid)", (err) => {
            if (err) {
              db.close();
              return reject(err);
            }
            checkComplete();
          });
        }
        
        if (pendingOperations === 0) {
          db.close();
          console.log('ℹ️ Paid status columns already exist in expenses table');
          resolve();
        }
      });
    });
  });
}
