import { api } from './api';
import { Expense, ExpenseParticipant } from '../types';

export interface GetExpensesResponse {
  success: boolean;
  expenses: Array<Expense & {
    payerName: string;
    participants: Array<ExpenseParticipant & { roommateName: string }>;
  }>;
}

export interface CreateExpenseRequest {
  amount: number;
  description: string;
  date: string;
  payerId: string;
  participants: ExpenseParticipant[];
}

export interface CreateExpenseResponse {
  success: boolean;
  expense: Expense & {
    payerName: string;
    participants: Array<ExpenseParticipant & { roommateName: string }>;
  };
}

export interface GetExpenseResponse {
  success: boolean;
  expense: Expense & {
    payerName: string;
    participants: Array<ExpenseParticipant & { roommateName: string }>;
  };
}

// Get all expenses for a house
export async function getExpenses(houseId: string, limit = 50, offset = 0): Promise<GetExpensesResponse> {
  return api.get<GetExpensesResponse>(`/expenses/house/${houseId}?limit=${limit}&offset=${offset}`);
}

// Create a new expense
export async function createExpense(data: CreateExpenseRequest): Promise<CreateExpenseResponse> {
  return api.post<CreateExpenseResponse>('/expenses', data);
}

// Get expense by ID
export async function getExpense(expenseId: string): Promise<GetExpenseResponse> {
  return api.get<GetExpenseResponse>(`/expenses/${expenseId}`);
}

// Delete expense (admin or creator only)
export async function deleteExpense(expenseId: string): Promise<{ success: boolean; message: string }> {
  return api.delete(`/expenses/${expenseId}`);
}
