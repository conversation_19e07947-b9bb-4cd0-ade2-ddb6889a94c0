# House Expenser

A modern expense tracking application for roommates to split bills and manage shared expenses.

## Features

- 🏠 **House Management** - Create and manage houses with multiple roommates
- 👥 **Roommate System** - Simple authentication without passwords for roommates
- 💰 **Expense Tracking** - Add expenses with flexible splitting options
- 📊 **Balance Calculations** - Real-time balance tracking and settlement suggestions
- 🔐 **Admin Controls** - Secure admin access for house management
- 🌐 **Full-Stack** - Complete backend API with database persistence

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <YOUR_GIT_URL>
cd house-expenser
```

2. Install frontend dependencies:
```bash
npm install
```

3. Install backend dependencies:
```bash
cd Backend
npm install
```

4. Set up the database:
```bash
npm run init-db
```

5. Start the backend server:
```bash
npm run dev
```

6. In a new terminal, start the frontend:
```bash
cd ..
npm run dev
```

The frontend will be available at `http://localhost:8081` and the backend API at `http://localhost:3001`.

## Usage

### Admin Setup
1. Go to Admin Login
2. Create a new house with a name, currency, and admin password
3. Login with the house ID and password
4. Add roommates to your house

### Roommate Access
1. Go to Roommate Login
2. Enter the house ID and your name
3. Start adding expenses and tracking balances

## Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development
- **Tailwind CSS** for styling
- **shadcn/ui** for UI components
- **React Router** for navigation
- **React Query** for state management

### Backend
- **Node.js** with Express.js
- **SQLite** database
- **JWT** authentication
- **bcrypt** for password hashing
- **CORS** and security middleware

## API Documentation

The backend provides RESTful API endpoints:

- `POST /api/auth/admin/login` - Admin authentication
- `POST /api/auth/roommate/login` - Roommate authentication
- `GET /api/houses/:id` - Get house details
- `POST /api/houses` - Create new house
- `GET /api/roommates/house/:houseId` - Get roommates
- `POST /api/roommates` - Add roommate
- `GET /api/expenses/house/:houseId` - Get expenses
- `POST /api/expenses` - Add expense
- `GET /api/balances/house/:houseId` - Get balances and settlements

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details
