import express from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { body, validationResult } from 'express-validator';
import { getRow, runQuery, getAllRows } from '../database/init.js';

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// Validation middleware
const validateAdminLogin = [
  body('houseId').notEmpty().withMessage('House ID is required'),
  body('password').isLength({ min: 4 }).withMessage('Password must be at least 4 characters')
];

const validateRoommateLogin = [
  body('houseId').notEmpty().withMessage('House ID is required'),
  body('roommateName').notEmpty().withMessage('Roommate name is required')
];

// Admin login
router.post('/admin/login', validateAdminLogin, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { houseId, password } = req.body;

    // Get house with admin password
    const house = await getRow(
      'SELECT id, name, currency, admin_password_hash FROM houses WHERE id = ?',
      [houseId]
    );

    if (!house) {
      return res.status(404).json({
        error: 'House not found'
      });
    }

    // Check if admin password is set
    if (!house.admin_password_hash) {
      return res.status(400).json({
        error: 'Admin password not set for this house'
      });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, house.admin_password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Invalid password'
      });
    }

    // Create session
    const sessionId = uuidv4();
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    await runQuery(
      'INSERT INTO sessions (id, house_id, is_admin, expires_at) VALUES (?, ?, ?, ?)',
      [sessionId, houseId, true, expiresAt.toISOString()]
    );

    // Generate JWT token
    const token = jwt.sign(
      { 
        sessionId, 
        houseId, 
        isAdmin: true 
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      success: true,
      token,
      user: {
        isAdmin: true,
        houseId,
        houseName: house.name
      }
    });

  } catch (error) {
    console.error('Admin login error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Roommate login
router.post('/roommate/login', validateRoommateLogin, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { houseId, roommateName } = req.body;

    // Check if house exists
    const house = await getRow(
      'SELECT id, name, currency FROM houses WHERE id = ?',
      [houseId]
    );

    if (!house) {
      return res.status(404).json({
        error: 'House not found'
      });
    }

    // Check if roommate exists
    const roommate = await getRow(
      'SELECT id, name FROM roommates WHERE house_id = ? AND name = ?',
      [houseId, roommateName]
    );

    if (!roommate) {
      return res.status(404).json({
        error: 'Roommate not found in this house'
      });
    }

    // Create session
    const sessionId = uuidv4();
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    await runQuery(
      'INSERT INTO sessions (id, house_id, roommate_id, is_admin, expires_at) VALUES (?, ?, ?, ?, ?)',
      [sessionId, houseId, roommate.id, false, expiresAt.toISOString()]
    );

    // Generate JWT token
    const token = jwt.sign(
      { 
        sessionId, 
        houseId, 
        roommateId: roommate.id,
        roommateName: roommate.name,
        isAdmin: false 
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      success: true,
      token,
      user: {
        isAdmin: false,
        houseId,
        houseName: house.name,
        roommateId: roommate.id,
        roommateName: roommate.name
      }
    });

  } catch (error) {
    console.error('Roommate login error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Logout
router.post('/logout', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (token) {
      const decoded = jwt.verify(token, JWT_SECRET);
      
      // Delete session from database
      await runQuery(
        'DELETE FROM sessions WHERE id = ?',
        [decoded.sessionId]
      );
    }

    res.json({
      success: true,
      message: 'Logged out successfully'
    });

  } catch (error) {
    // Even if token verification fails, we consider logout successful
    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  }
});

// Verify token
router.get('/verify', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        error: 'No token provided'
      });
    }

    const decoded = jwt.verify(token, JWT_SECRET);
    
    // Check if session exists and is not expired
    const session = await getRow(
      'SELECT * FROM sessions WHERE id = ? AND expires_at > datetime("now")',
      [decoded.sessionId]
    );

    if (!session) {
      return res.status(401).json({
        error: 'Session expired or invalid'
      });
    }

    // Get house info
    const house = await getRow(
      'SELECT id, name, currency FROM houses WHERE id = ?',
      [session.house_id]
    );

    let user = {
      isAdmin: session.is_admin,
      houseId: session.house_id,
      houseName: house.name
    };

    // If not admin, get roommate info
    if (!session.is_admin && session.roommate_id) {
      const roommate = await getRow(
        'SELECT id, name FROM roommates WHERE id = ?',
        [session.roommate_id]
      );
      
      if (roommate) {
        user.roommateId = roommate.id;
        user.roommateName = roommate.name;
      }
    }

    res.json({
      success: true,
      user
    });

  } catch (error) {
    console.error('Token verification error:', error);
    res.status(401).json({
      error: 'Invalid token'
    });
  }
});

export default router;
