import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Balance } from '@/types';
import { formatCurrency } from '@/utils/calculations';

interface BalanceDisplayProps {
  balances: Balance[];
  currency: string;
}

export const BalanceDisplay = ({ balances, currency }: BalanceDisplayProps) => {
  return (
    <Card className="shadow-card">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          💰 Current Balances
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {balances.length === 0 ? (
          <p className="text-muted-foreground text-center py-4">
            No balances yet. Add some expenses to get started!
          </p>
        ) : (
          balances.map((balance) => (
            <div key={balance.roommateId} className="flex justify-between items-center p-3 rounded-lg bg-muted/50">
              <span className="font-medium">{balance.roommateName}</span>
              <Badge 
                variant={balance.balance >= 0 ? "secondary" : "destructive"}
                className="font-mono"
              >
                {balance.balance >= 0 ? '+' : ''}{formatCurrency(balance.balance, currency)}
              </Badge>
            </div>
          ))
        )}
        {balances.length > 0 && (
          <div className="pt-2 text-xs text-muted-foreground">
            <p>• Green = you're owed money</p>
            <p>• Red = you owe money</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};