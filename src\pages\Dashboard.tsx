import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useApi } from '@/hooks/useApi';
import { ExpenseForm } from '@/components/ExpenseForm';
import { BalanceDisplay } from '@/components/BalanceDisplay';
import { ActivityFeed } from '@/components/ActivityFeed';
import { SettlementView } from '@/components/SettlementView';
import { getRoommates } from '@/services/roommates';
import { getExpenses } from '@/services/expenses';
import { getBalances } from '@/services/balances';
import { logout } from '@/services/auth';
import { Balance } from '@/types';
import { formatCurrency } from '@/utils/calculations';

const Dashboard = () => {
  const [showExpenseForm, setShowExpenseForm] = useState(false);
  const [showSettlement, setShowSettlement] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, logout: authLogout } = useAuth();

  // Redirect if not authenticated
  useEffect(() => {
    if (!user) {
      toast({
        title: "Access required",
        description: "Please log in to continue.",
        variant: "destructive",
      });
      navigate('/roommate');
      return;
    }
  }, [user, navigate, toast]);

  // Fetch data
  const { data: roommates, refetch: refetchRoommates } = useApi(
    () => getRoommates(user?.houseId || ''),
    { immediate: !!user?.houseId }
  );

  const { data: expenses, refetch: refetchExpenses } = useApi(
    () => getExpenses(user?.houseId || ''),
    { immediate: !!user?.houseId }
  );

  const { data: balancesData, refetch: refetchBalances } = useApi(
    () => getBalances(user?.houseId || ''),
    { immediate: !!user?.houseId }
  );

  const handleLogout = async () => {
    try {
      await logout();
      authLogout();
      navigate('/roommate');
    } catch (error) {
      // Even if logout fails, clear local state
      authLogout();
      navigate('/roommate');
    }
  };

  const handleExpenseAdded = () => {
    refetchExpenses();
    refetchBalances();
    setShowExpenseForm(false);
  };

  if (!user) {
    return null;
  }

  const balances = balancesData?.balances || [];
  const settlements = balancesData?.settlements || [];
  const currentBalance = balances.find(b => b.roommateId === user.roommateId);

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-gradient-primary text-white p-4">
        <div className="max-w-4xl mx-auto flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">{user.houseName}</h1>
            <p className="text-white/80">Welcome back, {user.roommateName}!</p>
          </div>
          <div className="flex items-center gap-3">
            {currentBalance && (
              <Badge
                variant={currentBalance.balance >= 0 ? "secondary" : "destructive"}
                className="text-sm px-3 py-1"
              >
                {currentBalance.balance >= 0 ? '+' : ''}{formatCurrency(currentBalance.balance, '₺')}
              </Badge>
            )}
            <Button variant="ghost" onClick={handleLogout} className="text-white hover:bg-white/20">
              Switch User
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-4 space-y-6">
        {/* Quick Actions */}
        <div className="flex flex-wrap gap-3">
          <Button 
            onClick={() => setShowExpenseForm(!showExpenseForm)}
            variant="warm"
            className="shadow-button"
          >
            {showExpenseForm ? 'Cancel' : '✨ Add Expense'}
          </Button>
          <Button 
            onClick={() => setShowSettlement(!showSettlement)}
            variant="secondary"
            className="shadow-button"
          >
            💰 Settle Up
          </Button>
          <Button 
            onClick={() => navigate('/admin')}
            variant="outline"
          >
            Admin Panel
          </Button>
        </div>

        {/* Expense Form */}
        {showExpenseForm && (
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle>Add New Expense</CardTitle>
            </CardHeader>
            <CardContent>
              <ExpenseForm
                onClose={handleExpenseAdded}
                currentUser={user.roommateName || ''}
              />
            </CardContent>
          </Card>
        )}

        {/* Settlement View */}
        {showSettlement && (
          <SettlementView
            balances={balances}
            settlements={settlements}
            currency="₺"
            onClose={() => setShowSettlement(false)}
          />
        )}

        <div className="grid md:grid-cols-2 gap-6">
          {/* Balances */}
          <BalanceDisplay
            balances={balances}
            currency="₺"
          />

          {/* Recent Activity */}
          <ActivityFeed
            expenses={expenses?.expenses?.slice(-10) || []}
            roommates={roommates?.roommates || []}
            currency="₺"
            onExpenseUpdated={() => {
              refetchExpenses();
              refetchBalances();
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;