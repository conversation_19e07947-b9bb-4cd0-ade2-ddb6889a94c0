import { Expense, Balance, Settlement, Roommate } from '@/types';

export function calculateBalances(expenses: Expense[], roommates: Roommate[]): Balance[] {
  const balanceMap = new Map<string, number>();
  
  // Initialize all roommates with 0 balance
  roommates.forEach(roommate => {
    balanceMap.set(roommate.id, 0);
  });

  // Calculate balances from expenses
  expenses.forEach(expense => {
    const totalAmount = expense.amount;
    const paidAmount = totalAmount;
    
    // Payer gets credited for paying
    const currentPayerBalance = balanceMap.get(expense.payerId) || 0;
    balanceMap.set(expense.payerId, currentPayerBalance + paidAmount);
    
    // Each participant gets debited for their share
    expense.participants.forEach(participant => {
      const currentBalance = balanceMap.get(participant.roommateId) || 0;
      balanceMap.set(participant.roommateId, currentBalance - participant.amount);
    });
  });

  // Convert to Balance array
  return roommates.map(roommate => ({
    roommateId: roommate.id,
    roommateName: roommate.name,
    balance: balanceMap.get(roommate.id) || 0
  }));
}

export function calculateSettlements(balances: Balance[]): Settlement[] {
  const debtors = balances.filter(b => b.balance < 0).sort((a, b) => a.balance - b.balance);
  const creditors = balances.filter(b => b.balance > 0).sort((a, b) => b.balance - a.balance);
  
  const settlements: Settlement[] = [];
  let i = 0, j = 0;
  
  while (i < debtors.length && j < creditors.length) {
    const debt = Math.abs(debtors[i].balance);
    const credit = creditors[j].balance;
    const amount = Math.min(debt, credit);
    
    if (amount > 0.01) { // Avoid tiny settlements
      settlements.push({
        from: debtors[i].roommateId,
        to: creditors[j].roommateId,
        fromName: debtors[i].roommateName,
        toName: creditors[j].roommateName,
        amount
      });
    }
    
    debtors[i].balance += amount;
    creditors[j].balance -= amount;
    
    if (Math.abs(debtors[i].balance) < 0.01) i++;
    if (Math.abs(creditors[j].balance) < 0.01) j++;
  }
  
  return settlements;
}

export function formatCurrency(amount: number, currency: string = '₺'): string {
  const absAmount = Math.abs(amount);
  const formattedAmount = absAmount.toFixed(2);
  return `${formattedAmount} ${currency}`;
}