import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { roommateLogin } from '@/services/auth';
import { Roommate } from '@/types';

const RoommateLogin = () => {
  const [houseId, setHouseId] = useState('');
  const [roommateName, setRoommateName] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { setUser } = useAuth();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!houseId.trim() || !roommateName.trim()) return;

    setLoading(true);

    try {
      const response = await roommateLogin(houseId.trim(), roommateName.trim());

      if (response.success) {
        setUser(response.user);
        toast({
          title: "Welcome back!",
          description: `Successfully logged in to ${response.user.houseName}.`,
        });
        navigate('/dashboard');
      }
    } catch (error: any) {
      toast({
        title: "Login failed",
        description: error.message || "Invalid house ID or roommate name.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-secondary flex items-center justify-center p-4">
      <Card className="w-full max-w-md shadow-card bg-card">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-secondary">
            Welcome Home! 🏠
          </CardTitle>
          <CardDescription className="text-muted-foreground">
            Choose your name to continue
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="houseId">House ID</Label>
              <Input
                id="houseId"
                type="text"
                value={houseId}
                onChange={(e) => setHouseId(e.target.value)}
                placeholder="Enter house ID"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="roommateName">Your Name</Label>
              <Input
                id="roommateName"
                type="text"
                value={roommateName}
                onChange={(e) => setRoommateName(e.target.value)}
                placeholder="Enter your name"
                required
              />
            </div>
            <Button
              type="submit"
              className="w-full shadow-button"
              disabled={loading}
            >
              {loading ? 'Signing in...' : 'Continue'}
            </Button>
          </form>

          <div className="text-center">
            <Button 
              type="button" 
              variant="ghost" 
              onClick={() => navigate('/admin')}
              className="text-sm"
            >
              Admin Login
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RoommateLogin;