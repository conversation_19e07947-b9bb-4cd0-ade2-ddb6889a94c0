import { api } from './api';
import { Balance, Settlement } from '../types';

export interface GetBalancesResponse {
  success: boolean;
  balances: Balance[];
  settlements: Settlement[];
}

export interface GetRoommateBalanceResponse {
  success: boolean;
  balance: Balance;
}

export interface GetExpenseSummaryResponse {
  success: boolean;
  summary: {
    totalAmount: number;
    totalCount: number;
    expensesByPayer: Array<{
      roommateId: string;
      roommateName: string;
      totalPaid: number;
      expenseCount: number;
    }>;
    recentExpenses: Array<{
      id: string;
      amount: number;
      description: string;
      date: string;
      payerName: string;
      loggedBy: string;
    }>;
  };
}

// Get balances and settlements for a house
export async function getBalances(houseId: string): Promise<GetBalancesResponse> {
  return api.get<GetBalancesResponse>(`/balances/house/${houseId}`);
}

// Get balance for a specific roommate
export async function getRoommateBalance(roommateId: string): Promise<GetRoommateBalanceResponse> {
  return api.get<GetRoommateBalanceResponse>(`/balances/roommate/${roommateId}`);
}

// Get expense summary for a house
export async function getExpenseSummary(houseId: string): Promise<GetExpenseSummaryResponse> {
  return api.get<GetExpenseSummaryResponse>(`/balances/summary/${houseId}`);
}
