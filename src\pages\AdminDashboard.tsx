import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useApi, useMutation } from '@/hooks/useApi';
import { createHouse, getHouseStats } from '@/services/houses';
import { getRoommates, createRoommate, deleteRoommate } from '@/services/roommates';
import { logout } from '@/services/auth';
import { House, Roommate } from '@/types';

const AdminDashboard = () => {
  const [newRoommateName, setNewRoommateName] = useState('');
  const [houseName, setHouseName] = useState('');
  const [houseCurrency, setHouseCurrency] = useState('₺');
  const [adminPassword, setAdminPassword] = useState('');
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, logout: authLogout } = useAuth();

  // Redirect if not authenticated as admin
  useEffect(() => {
    if (!user || !user.isAdmin) {
      navigate('/admin');
      return;
    }
  }, [user, navigate]);

  // Fetch data
  const { data: roommates, refetch: refetchRoommates } = useApi(
    () => getRoommates(user?.houseId || ''),
    { immediate: !!user?.houseId }
  );

  const { data: stats, refetch: refetchStats } = useApi(
    () => getHouseStats(user?.houseId || ''),
    { immediate: !!user?.houseId }
  );

  // Mutations
  const createHouseMutation = useMutation(createHouse);
  const createRoommateMutation = useMutation(createRoommate);
  const deleteRoommateMutation = useMutation(deleteRoommate);

  const handleCreateHouse = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!houseName.trim() || !adminPassword.trim()) return;

    try {
      const response = await createHouseMutation.mutate({
        name: houseName.trim(),
        currency: houseCurrency,
        adminPassword: adminPassword.trim()
      });

      if (response.success) {
        toast({
          title: "🏠 House created!",
          description: `${response.house.name} is ready for roommates.`,
        });
        setHouseName('');
        setAdminPassword('');
        // User will need to login again with the new house
        navigate('/admin');
      }
    } catch (error: any) {
      toast({
        title: "Failed to create house",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleAddRoommate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newRoommateName.trim() || !user?.houseId) return;

    try {
      const response = await createRoommateMutation.mutate({
        name: newRoommateName.trim(),
        houseId: user.houseId
      });

      if (response.success) {
        setNewRoommateName('');
        refetchRoommates();
        refetchStats();

        toast({
          title: "✅ Roommate added!",
          description: `${response.roommate.name} can now log in and add expenses.`,
        });
      }
    } catch (error: any) {
      toast({
        title: "Failed to add roommate",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleDeleteRoommate = async (id: string) => {
    try {
      const response = await deleteRoommateMutation.mutate(id);

      if (response.success) {
        refetchRoommates();
        refetchStats();

        toast({
          title: "Roommate removed",
          description: response.message,
        });
      }
    } catch (error: any) {
      toast({
        title: "Failed to delete roommate",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      authLogout();
      navigate('/admin');
    } catch (error) {
      // Even if logout fails, clear local state
      authLogout();
      navigate('/admin');
    }
  };

  const exportData = () => {
    const data = {
      house: user ? { id: user.houseId, name: user.houseName } : null,
      roommates: roommates?.roommates || [],
      stats: stats?.stats || {},
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `house-wallet-${user?.houseName || 'data'}-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);

    toast({
      title: "Data exported",
      description: "Your house data has been downloaded.",
    });
  };

  if (!user || !user.isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="bg-gradient-primary text-white p-4">
        <div className="max-w-4xl mx-auto flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">Admin Dashboard</h1>
            <p className="text-white/80">Manage your house and roommates</p>
          </div>
          <div className="flex gap-3">
            <Button variant="ghost" onClick={() => navigate('/dashboard')} className="text-white hover:bg-white/20">
              View Dashboard
            </Button>
            <Button variant="ghost" onClick={handleLogout} className="text-white hover:bg-white/20">
              Logout
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-4 space-y-6">
        {/* House Setup */}
        {!user.houseId ? (
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle>🏠 Create Your House</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleCreateHouse} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="houseName">House Name</Label>
                    <Input
                      id="houseName"
                      value={houseName}
                      onChange={(e) => setHouseName(e.target.value)}
                      placeholder="e.g., The Awesome House"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="currency">Currency</Label>
                    <Select value={houseCurrency} onValueChange={setHouseCurrency}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="₺">₺ Turkish Lira</SelectItem>
                        <SelectItem value="$">$ US Dollar</SelectItem>
                        <SelectItem value="€">€ Euro</SelectItem>
                        <SelectItem value="£">£ British Pound</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="adminPassword">Admin Password</Label>
                  <Input
                    id="adminPassword"
                    type="password"
                    value={adminPassword}
                    onChange={(e) => setAdminPassword(e.target.value)}
                    placeholder="Set admin password"
                    required
                  />
                </div>
                <Button
                  type="submit"
                  variant="warm"
                  className="w-full"
                  disabled={createHouseMutation.loading}
                >
                  {createHouseMutation.loading ? 'Creating...' : 'Create House'}
                </Button>
              </form>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* House Info */}
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  🏠 {user.houseName}
                  <Badge variant="secondary">House ID: {user.houseId}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-3">
                  <Button onClick={exportData} variant="outline" size="sm">
                    📊 Export Data
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Roommate Management */}
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>👥 Roommates ({roommates?.roommates?.length || 0})</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <form onSubmit={handleAddRoommate} className="flex gap-2">
                  <Input
                    value={newRoommateName}
                    onChange={(e) => setNewRoommateName(e.target.value)}
                    placeholder="Enter roommate name"
                    required
                  />
                  <Button
                    type="submit"
                    variant="default"
                    disabled={createRoommateMutation.loading}
                  >
                    {createRoommateMutation.loading ? 'Adding...' : 'Add'}
                  </Button>
                </form>

                {roommates?.roommates && roommates.roommates.length > 0 && (
                  <div className="space-y-2">
                    {roommates.roommates.map((roommate) => (
                      <div key={roommate.id} className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                        <span className="font-medium">{roommate.name}</span>
                        <Button
                          onClick={() => handleDeleteRoommate(roommate.id)}
                          variant="destructive"
                          size="sm"
                          disabled={deleteRoommateMutation.loading}
                        >
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Stats */}
            <div className="grid md:grid-cols-3 gap-4">
              <Card className="shadow-card">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-primary">{stats?.stats?.roommateCount || 0}</div>
                  <div className="text-sm text-muted-foreground">Roommates</div>
                </CardContent>
              </Card>
              <Card className="shadow-card">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-primary">{stats?.stats?.expenseCount || 0}</div>
                  <div className="text-sm text-muted-foreground">Total Expenses</div>
                </CardContent>
              </Card>
              <Card className="shadow-card">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-primary">
                    {(stats?.stats?.totalExpenses || 0).toFixed(2)}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Amount</div>
                </CardContent>
              </Card>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default AdminDashboard;