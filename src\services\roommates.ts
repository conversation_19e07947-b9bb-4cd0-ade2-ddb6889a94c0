import { api } from './api';
import { Roommate } from '../types';

export interface GetRoommatesResponse {
  success: boolean;
  roommates: Roommate[];
}

export interface CreateRoommateRequest {
  name: string;
  houseId: string;
}

export interface CreateRoommateResponse {
  success: boolean;
  roommate: Roommate;
}

export interface GetRoommateResponse {
  success: boolean;
  roommate: Roommate;
}

export interface UpdateRoommateRequest {
  name: string;
}

// Get all roommates for a house
export async function getRoommates(houseId: string): Promise<GetRoommatesResponse> {
  return api.get<GetRoommatesResponse>(`/roommates/house/${houseId}`);
}

// Create a new roommate (admin only)
export async function createRoommate(data: CreateRoommateRequest): Promise<CreateRoommateResponse> {
  return api.post<CreateRoommateResponse>('/roommates', data);
}

// Get roommate by ID
export async function getRoommate(roommateId: string): Promise<GetRoommateResponse> {
  return api.get<GetRoommateResponse>(`/roommates/${roommateId}`);
}

// Update roommate (admin only)
export async function updateRoommate(roommateId: string, data: UpdateRoommateRequest): Promise<GetRoommateResponse> {
  return api.put<GetRoommateResponse>(`/roommates/${roommateId}`, data);
}

// Delete roommate (admin only)
export async function deleteRoommate(roommateId: string): Promise<{ success: boolean; message: string }> {
  return api.delete(`/roommates/${roommateId}`);
}
