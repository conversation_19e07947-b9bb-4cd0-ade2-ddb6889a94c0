import express from 'express';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import { body, validationResult } from 'express-validator';
import { getRow, runQuery, getAllRows } from '../database/init.js';
import { authenticateToken, requireAdmin } from '../middleware/auth.js';

const router = express.Router();

// Validation middleware
const validateCreateHouse = [
  body('name').trim().isLength({ min: 1 }).withMessage('House name is required'),
  body('currency').isLength({ min: 1 }).withMessage('Currency is required'),
  body('adminPassword').isLength({ min: 4 }).withMessage('Admin password must be at least 4 characters')
];

const validateUpdateHouse = [
  body('name').optional().trim().isLength({ min: 1 }).withMessage('House name cannot be empty'),
  body('currency').optional().isLength({ min: 1 }).withMessage('Currency cannot be empty')
];

// Create a new house
router.post('/', validateCreateHouse, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { name, currency, adminPassword } = req.body;
    const houseId = uuidv4();

    // Hash admin password
    const saltRounds = 10;
    const adminPasswordHash = await bcrypt.hash(adminPassword, saltRounds);

    // Create house
    await runQuery(
      'INSERT INTO houses (id, name, currency, admin_password_hash) VALUES (?, ?, ?, ?)',
      [houseId, name, currency, adminPasswordHash]
    );

    // Get created house
    const house = await getRow(
      'SELECT id, name, currency, created_at FROM houses WHERE id = ?',
      [houseId]
    );

    res.status(201).json({
      success: true,
      house: {
        id: house.id,
        name: house.name,
        currency: house.currency,
        createdAt: house.created_at
      }
    });

  } catch (error) {
    console.error('Create house error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Get house by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const house = await getRow(
      'SELECT id, name, currency, created_at FROM houses WHERE id = ?',
      [id]
    );

    if (!house) {
      return res.status(404).json({
        error: 'House not found'
      });
    }

    res.json({
      success: true,
      house: {
        id: house.id,
        name: house.name,
        currency: house.currency,
        createdAt: house.created_at
      }
    });

  } catch (error) {
    console.error('Get house error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Update house (admin only)
router.put('/:id', authenticateToken, requireAdmin, validateUpdateHouse, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { id } = req.params;
    const { name, currency } = req.body;

    // Check if house exists and user has access
    if (req.user.houseId !== id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    // Build update query dynamically
    const updates = [];
    const values = [];

    if (name !== undefined) {
      updates.push('name = ?');
      values.push(name);
    }

    if (currency !== undefined) {
      updates.push('currency = ?');
      values.push(currency);
    }

    if (updates.length === 0) {
      return res.status(400).json({
        error: 'No valid fields to update'
      });
    }

    values.push(id);

    const result = await runQuery(
      `UPDATE houses SET ${updates.join(', ')} WHERE id = ?`,
      values
    );

    if (result.changes === 0) {
      return res.status(404).json({
        error: 'House not found'
      });
    }

    // Get updated house
    const house = await getRow(
      'SELECT id, name, currency, created_at FROM houses WHERE id = ?',
      [id]
    );

    res.json({
      success: true,
      house: {
        id: house.id,
        name: house.name,
        currency: house.currency,
        createdAt: house.created_at
      }
    });

  } catch (error) {
    console.error('Update house error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Delete house (admin only)
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if house exists and user has access
    if (req.user.houseId !== id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    const result = await runQuery(
      'DELETE FROM houses WHERE id = ?',
      [id]
    );

    if (result.changes === 0) {
      return res.status(404).json({
        error: 'House not found'
      });
    }

    res.json({
      success: true,
      message: 'House deleted successfully'
    });

  } catch (error) {
    console.error('Delete house error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Get house statistics (admin only)
router.get('/:id/stats', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if house exists and user has access
    if (req.user.houseId !== id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    // Get roommate count
    const roommateCount = await getRow(
      'SELECT COUNT(*) as count FROM roommates WHERE house_id = ?',
      [id]
    );

    // Get expense count and total
    const expenseStats = await getRow(
      'SELECT COUNT(*) as count, COALESCE(SUM(amount), 0) as total FROM expenses WHERE house_id = ?',
      [id]
    );

    res.json({
      success: true,
      stats: {
        roommateCount: roommateCount.count,
        expenseCount: expenseStats.count,
        totalExpenses: expenseStats.total
      }
    });

  } catch (error) {
    console.error('Get house stats error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

export default router;
