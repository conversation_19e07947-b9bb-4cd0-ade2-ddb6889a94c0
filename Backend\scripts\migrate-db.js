import { getDatabase } from '../src/database/init.js';

async function migrateDatabase() {
  const db = await getDatabase();
  
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      console.log('🔄 Running database migrations...');
      
      // Check if username column exists
      db.all("PRAGMA table_info(houses)", (err, columns) => {
        if (err) {
          reject(err);
          return;
        }
        
        const hasUsername = columns.some(col => col.name === 'username');
        
        if (!hasUsername) {
          console.log('📝 Adding username column to houses table...');
          db.run(`ALTER TABLE houses ADD COLUMN username TEXT`, (err) => {
            if (err) {
              console.error('Error adding username column:', err);
              reject(err);
              return;
            }
            
            // Populate username for existing houses
            console.log('📝 Populating usernames for existing houses...');
            db.run(`UPDATE houses SET username = 'house_' || id WHERE username IS NULL OR username = ''`, (err) => {
              if (err) {
                console.error('Error populating usernames:', err);
                reject(err);
                return;
              }
              
              // Add unique constraint
              console.log('📝 Adding unique constraint to username...');
              db.run(`CREATE UNIQUE INDEX IF NOT EXISTS idx_houses_username_unique ON houses (username)`, (err) => {
                if (err) {
                  console.error('Error adding unique constraint:', err);
                  reject(err);
                  return;
                }
                
                migrateExpenses();
              });
            });
          });
        } else {
          console.log('✅ Username column already exists');
          migrateExpenses();
        }
      });
      
      function migrateExpenses() {
        // Check if payment tracking columns exist
        db.all("PRAGMA table_info(expenses)", (err, columns) => {
          if (err) {
            reject(err);
            return;
          }
          
          const hasIsPaid = columns.some(col => col.name === 'is_paid');
          const hasPaidAt = columns.some(col => col.name === 'paid_at');
          const hasPaidBy = columns.some(col => col.name === 'paid_by');
          
          let pendingOperations = 0;
          let completedOperations = 0;
          
          function checkComplete() {
            completedOperations++;
            if (completedOperations === pendingOperations) {
              db.close((err) => {
                if (err) {
                  reject(err);
                } else {
                  console.log('✅ Database migrations completed successfully');
                  resolve();
                }
              });
            }
          }
          
          if (!hasIsPaid) {
            pendingOperations++;
            console.log('📝 Adding is_paid column to expenses table...');
            db.run(`ALTER TABLE expenses ADD COLUMN is_paid BOOLEAN DEFAULT FALSE`, (err) => {
              if (err) {
                console.error('Error adding is_paid column:', err);
                reject(err);
                return;
              }
              checkComplete();
            });
          } else {
            console.log('✅ is_paid column already exists');
          }
          
          if (!hasPaidAt) {
            pendingOperations++;
            console.log('📝 Adding paid_at column to expenses table...');
            db.run(`ALTER TABLE expenses ADD COLUMN paid_at DATETIME`, (err) => {
              if (err) {
                console.error('Error adding paid_at column:', err);
                reject(err);
                return;
              }
              checkComplete();
            });
          } else {
            console.log('✅ paid_at column already exists');
          }
          
          if (!hasPaidBy) {
            pendingOperations++;
            console.log('📝 Adding paid_by column to expenses table...');
            db.run(`ALTER TABLE expenses ADD COLUMN paid_by TEXT`, (err) => {
              if (err) {
                console.error('Error adding paid_by column:', err);
                reject(err);
                return;
              }
              checkComplete();
            });
          } else {
            console.log('✅ paid_by column already exists');
          }
          
          // If no operations are pending, close the database
          if (pendingOperations === 0) {
            db.close((err) => {
              if (err) {
                reject(err);
              } else {
                console.log('✅ All columns already exist, no migrations needed');
                resolve();
              }
            });
          }
        });
      }
    });
  });
}

async function main() {
  try {
    await migrateDatabase();
    console.log('✅ Database migration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Database migration failed:', error);
    process.exit(1);
  }
}

main();
